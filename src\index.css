@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --primary-light: #eef2ff;
  --error-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --text-color: #111827;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --border-color: #e5e7eb;
  --border-focus: #c7d2fe;
  --background-color: #ffffff;
  --background-secondary: #f8fafc;
}

/* Estilos adicionales para el dashboard */
/*
.notifications-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  max-width: 400px;
}
*/
body {
  font-family: 'Inter', system-ui, sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: var(--text-color);
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Tarjeta principal */
.auth-card {
  background: white;
  border-radius: 1rem;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
  padding: 2.5rem;
  width: 100%;
  max-width: 420px;
  position: relative;
  backdrop-filter: blur(10px);
}

/* Tarjeta más ancha para registro */
.auth-card.register-card {
  max-width: 650px;
  padding: 3rem;
}

/* Layout de dos columnas para registro */
.register-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.register-form-full-width {
  grid-column: 1 / -1;
}

/* Responsive para el grid de registro */
@media (max-width: 640px) {
  .register-form-grid {
    grid-template-columns: 1fr;
    gap: 0;
  }
}

/* Título */
.auth-title {
  font-family: 'Poppins', sans-serif;
  font-size: 1.875rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.auth-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: 2px;
}

/* Grupos de entrada */
.input-group {
  margin-bottom: 1.25rem;
}

/* Etiquetas */
.input-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  letter-spacing: 0.025em;
}

/* Contenedor de campo con iconos */
.input-container {
  position: relative;
}

/* Campos de entrada */
.input-field {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  color: var(--text-color);
  letter-spacing: 0.025em;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.input-field::placeholder {
  color: var(--text-light);
  font-weight: 400;
}

/* Estados de validación */
.input-field.valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.input-field.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

/* Campos con iconos */
.input-field.with-icon {
  padding-left: 3rem;
}

/* Iconos dentro de los campos */
.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  pointer-events: none;
  z-index: 1;
  transition: color 0.2s ease;
}



/* Toggle de contraseña */
.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--text-light);
  z-index: 3;
  transition: color 0.2s ease;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.password-toggle:hover {
  color: var(--primary-color);
  background: var(--primary-light);
}

/* Botón principal */
.submit-button {
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 1rem;
  box-shadow: 0 4px 14px 0 rgba(79, 70, 229, 0.25);
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(79, 70, 229, 0.35);
}

.submit-button:active {
  transform: translateY(0);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 14px 0 rgba(79, 70, 229, 0.15);
}

/* Texto de cambio de formulario */
.switch-form-text {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.switch-form-link {
  color: var(--primary-color);
  font-weight: 500;
  cursor: pointer;
  margin-left: 0.25rem;
}

.switch-form-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* Mensajes de feedback */
.feedback {
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.feedback.error {
  background: #fef2f2;
  color: var(--error-color);
  border: 1px solid #fecaca;
}

.feedback-icon {
  margin-right: 0.5rem;
}

/* Medidor de fuerza de contraseña */
.password-strength {
  margin-top: 0.5rem;
}

.password-strength-meter {
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.password-strength-meter-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.password-strength-meter-fill.weak {
  background: var(--error-color);
  width: 33%;
}

.password-strength-meter-fill.medium {
  background: var(--warning-color);
  width: 66%;
}

.password-strength-meter-fill.strong {
  background: var(--success-color);
  width: 100%;
}

.password-strength-text {
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
}

/* Indicaciones de contraseña */
.password-requirements {
  margin-top: 0.75rem;
  padding: 1rem;
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-size: 0.8rem;
}

.password-requirements-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.75rem;
  font-size: 0.85rem;
  letter-spacing: 0.025em;
}

.password-requirements-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem 1rem;
}

.password-requirement {
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
  transition: all 0.2s ease;
  border-radius: 0.375rem;
  font-weight: 500;
}

.password-requirement.met {
  color: var(--success-color);
}

.password-requirement.not-met {
  color: var(--text-secondary);
}

.password-requirement-icon {
  margin-right: 0.5rem;
  font-size: 0.85rem;
  min-width: 1.1rem;
}

/* Responsive para requisitos */
@media (max-width: 480px) {
  .password-requirements-list {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }

  .password-requirements {
    padding: 0.5rem;
    font-size: 0.7rem;
  }
}

/* Responsividad */
@media (max-width: 640px) {
  .auth-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .auth-title {
    font-size: 1.25rem;
  }
}


