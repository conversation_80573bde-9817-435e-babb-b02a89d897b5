import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();

  if (!user) {
    // Podrías redirigir al login o mostrar un loader/mensaje
    return <div className="p-8 text-center">Cargando información del usuario...</div>;
  }

  return (
    <div className="min-h-screen bg-red-500 text-white p-10">
      <h1 className="text-4xl font-bold mb-4">Admin Dashboard (Prueba Tailwind)</h1>
      <p className="mb-2">Usuario: {user.name} ({user.email})</p>
      <p className="mb-4">Rol: {user.role}</p>
      
      <div className="bg-blue-700 p-6 rounded-lg shadow-xl">
        <p>Este es un contenedor azul para probar Tailwind.</p>
      </div>

      <button
        onClick={logout}
        className="mt-6 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold py-2 px-4 rounded shadow"
      >
        <PERSON><PERSON><PERSON> (Prueba)
      </button>
    </div>
  );
};

export default AdminDashboard;
