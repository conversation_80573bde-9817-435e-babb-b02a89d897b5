import React, { useState, useEffect, lazy, Suspense } from 'react';
import type { FC } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import toast, { Toaster } from 'react-hot-toast';

// Interfaces de LoginForm y RegisterForm
interface LoginFormProps {
  onSubmit: (email: string, password: string) => void;
  onSwitchToRegister: () => void;
}

interface RegisterFormProps {
  onSubmit: (name: string, email: string, password: string) => void;
  onSwitchToLogin: () => void;
}

interface UserData {
  name: string;
  email: string;
}

// Importamos los componentes
const LoginForm = lazy(() => import('./LoginForm'));
const RegisterForm = lazy(() => import('./RegisterForm'));
const WelcomeScreen = lazy(() => import('./WelcomeScreen'));


const AuthContainer: FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  // const [notifications, setNotifications] = useState<Notification[]>([]); // Eliminado
  const { isAuthenticated, login, register } = useAuth();

  // Función para añadir notificaciones - Reemplazada por react-hot-toast
  // const addNotification = (notification: Omit<Notification, 'id'>) => {
  //   console.log('[DEBUG] addNotification called with:', notification);
  //   const id = Math.random().toString(36).substring(2, 9);
  //   setNotifications((prev) => {
  //     const newNotifications = [...prev, { ...notification, id }];
  //     console.log('[DEBUG] new notifications state:', newNotifications);
  //     return newNotifications;
  //   });
  // };

  // Función para cerrar notificaciones - Reemplazada por react-hot-toast
  // const closeNotification = (id: string) => {
  //   setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  // };

  const handleLoginSubmit = async (email: string, password: string) => {
    const loadingToastId = toast.loading('Iniciando sesión...');
    try {
      await login({ email, password });
      toast.success('¡Bienvenido! Has iniciado sesión correctamente.', { id: loadingToastId });
    } catch (error) {
      // console.error("Error en login:", error); // Opcional: para depurar el error original de Supabase
      toast.error('Credenciales de inicio de sesión inválidas. Por favor, verifica tu correo y contraseña.', { id: loadingToastId });
    }
  };

  const handleRegisterSubmit = async (name: string, email: string, password: string) => {
    const loadingToastId = toast.loading('Registrando usuario...');
    try {
      await register({ name, email, password, role: 'usuario' });
      toast.success('¡Registro exitoso! Tu cuenta ha sido creada. Ahora puedes iniciar sesión.', { id: loadingToastId });

      // Cambiamos a la pantalla de login después de un breve retraso
      setTimeout(() => {
        setIsLogin(true);
      }, 2000);
    } catch (error) {
      // console.error("Error en registro:", error); // Opcional: para depurar el error original de Supabase
      toast.error('Error al crear la cuenta. Por favor, inténtalo de nuevo.', { id: loadingToastId });
    }
  };

  // Si está autenticado, redirigir al dashboard
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <div className="auth-page">
      <Toaster position="bottom-right" reverseOrder={false} />
      <div className="w-full max-w-md px-4">
        <AnimatePresence mode="wait">
          {isLogin ? (
            <motion.div
              key="login"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Suspense fallback={
                <div className="auth-card flex items-center justify-center py-8">
                  <div className="flex flex-col items-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary-color mb-3"></div>
                    <p className="text-gray-600">Cargando...</p>
                  </div>
                </div>
              }>
                <LoginForm
                  onSubmit={handleLoginSubmit}
                  onSwitchToRegister={() => setIsLogin(false)}
                />
              </Suspense>
            </motion.div>
          ) : (
            <motion.div
              key="register"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Suspense fallback={
                <div className="auth-card flex items-center justify-center py-8">
                  <div className="flex flex-col items-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary-color mb-3"></div>
                    <p className="text-gray-600">Cargando...</p>
                  </div>
                </div>
              }>
                <RegisterForm
                  onSubmit={handleRegisterSubmit}
                  onSwitchToLogin={() => setIsLogin(true)}
                />
              </Suspense>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Contenedor de notificaciones */}
      {/* Contenedor de notificaciones - Reemplazado por <Toaster /> de react-hot-toast */}
      {/*
      <div className="fixed top-4 right-4 z-50 w-full max-w-xs space-y-3">
        <AnimatePresence initial={false}>
          {notifications.map((notification) => {
            console.log('[DEBUG] Mapping notification in AnimatePresence:', notification);
            return (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onClose={closeNotification}
              />
            );
          })}
        </AnimatePresence>
      </div>
      */}
    </div>
  );
};

export default AuthContainer;