import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import AuthContainer from './components/auth/AuthContainer';
import SimpleDashboard from './components/dashboard/SimpleDashboard';
import ProtectedRoute from './components/auth/ProtectedRoute';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="app">
          <Routes>
            {/* Ruta de login */}
            <Route path="/login" element={<AuthContainer />} />

            {/* Ruta del dashboard protegida */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute requiredRoles={['administrador', 'super_administrador', 'usuario']}>
                  <SimpleDashboard />
                </ProtectedRoute>
              }
            />

            {/* Ruta por defecto redirige al login */}
            <Route path="/" element={<Navigate to="/login" replace />} />

            {/* Ruta 404 */}
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
