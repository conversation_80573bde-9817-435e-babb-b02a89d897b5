import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';
import type {
  AuthContextType,
  AuthState,
  User,
  UserRole,
  LoginCredentials,
  RegisterData
} from '../types/auth';

// Estado inicial
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true
};

// Tipos de acciones
type AuthAction = 
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE' }
  | { type: 'LOGOUT' }
  | { type: 'REGISTER_START' }
  | { type: 'REGISTER_SUCCESS' }
  | { type: 'REGISTER_FAILURE' }
  | { type: 'SET_LOADING'; payload: boolean };

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
    case 'REGISTER_START':
      return { ...state, isLoading: true };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false
      };
    
    case 'LOGIN_FAILURE':
    case 'REGISTER_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false
      };
    
    case 'REGISTER_SUCCESS':
      return { ...state, isLoading: false };
    
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false
      };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    default:
      return state;
  }
};

// Crear contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook para usar el contexto
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

// Provider del contexto
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Verificar sesión de Supabase al cargar
  useEffect(() => {
    const checkSupabaseAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error al obtener sesión:', error);
          dispatch({ type: 'SET_LOADING', payload: false });
          return;
        }

        if (session?.user) {
          console.log('[AuthContext] Initial session check - user ID:', session.user.id);

          // Obtener el perfil del usuario
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          console.log('[AuthContext] Initial profile query result - data:', profile, 'error:', profileError);

          if (profileError) {
            console.error('Error al obtener perfil:', profileError);
            dispatch({ type: 'SET_LOADING', payload: false });
            return;
          }

          if (profile) {
            const user: User = {
              id: profile.id,
              name: profile.name,
              email: profile.email,
              role: profile.role as UserRole,
              createdAt: new Date(profile.created_at),
              lastLogin: new Date()
            };
            dispatch({ type: 'LOGIN_SUCCESS', payload: user });
          }
        } else {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      } catch (error) {
        console.error('Error al verificar autenticación:', error);
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    checkSupabaseAuth();

    // Escuchar cambios en la autenticación
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('[AuthContext] onAuthStateChange event:', event, 'session:', session);
        if (event === 'SIGNED_IN' && session?.user) {
          console.log('[AuthContext] SIGNED_IN event, user ID:', session.user.id);
          console.log('[AuthContext] Attempting to fetch profile for user ID:', session.user.id);

          // Add a small delay to ensure the JWT is fully processed
          await new Promise(resolve => setTimeout(resolve, 100));

          // Usuario se ha logueado
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          console.log('[AuthContext] Profile query result - data:', profile, 'error:', profileError);

          if (profileError) {
            console.error('[AuthContext] Error fetching profile in onAuthStateChange:', profileError);
            dispatch({ type: 'SET_LOADING', payload: false });
            return;
          }

          if (profile) {
            console.log('[AuthContext] Profile fetched:', profile);
            const user: User = {
              id: profile.id,
              name: profile.name,
              email: profile.email,
              role: profile.role as UserRole,
              createdAt: new Date(profile.created_at),
              lastLogin: new Date()
            };
            console.log('[AuthContext] Dispatching LOGIN_SUCCESS with user:', user);
            dispatch({ type: 'LOGIN_SUCCESS', payload: user });
          } else {
            console.warn('[AuthContext] SIGNED_IN event, but no profile found for user ID:', session.user.id);
          }
        } else if (event === 'SIGNED_OUT') {
          console.log('[AuthContext] SIGNED_OUT event, dispatching LOGOUT');
          // Usuario se ha deslogueado
          dispatch({ type: 'LOGOUT' });
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('No se pudo obtener información del usuario');
      }

      // El perfil se cargará automáticamente a través del listener onAuthStateChange
      // No necesitamos hacer dispatch aquí
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE' });
      throw error;
    }
  };

  const register = async (data: RegisterData): Promise<void> => {
    dispatch({ type: 'REGISTER_START' });

    try {
      const { data: authData, error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name,
            role: data.role || 'usuario'
          }
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!authData.user) {
        throw new Error('No se pudo crear el usuario');
      }

      dispatch({ type: 'REGISTER_SUCCESS' });
    } catch (error) {
      dispatch({ type: 'REGISTER_FAILURE' });
      throw error;
    }
  };

  const logout = async () => {
    try {
      await supabase.auth.signOut();
      // El dispatch se hará automáticamente a través del listener onAuthStateChange
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
      // Forzar logout local si hay error
      dispatch({ type: 'LOGOUT' });
    }
  };

  const hasRole = (role: UserRole): boolean => {
    return state.user?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return state.user ? roles.includes(state.user.role) : false;
  };

  const value: AuthContextType = {
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    login,
    register,
    logout,
    hasRole,
    hasAnyRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
