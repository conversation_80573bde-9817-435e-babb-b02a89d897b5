import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type {
  AuthContextType,
  User,
  UserRole,
  LoginCredentials,
  RegisterData
} from '../types/auth';



// Crear contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook para usar el contexto
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

// Función auxiliar para obtener el perfil del usuario
const fetchUserProfile = async (userId: string): Promise<User> => {
  console.log('[AuthContext] Fetching profile for user ID:', userId);

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('[AuthContext] Error fetching profile:', error);
    throw new Error(`Error al obtener perfil: ${error.message}`);
  }

  if (!profile) {
    throw new Error('Perfil de usuario no encontrado');
  }

  console.log('[AuthContext] Profile fetched successfully:', profile);

  return {
    id: profile.id,
    name: profile.name,
    email: profile.email,
    role: profile.role as UserRole,
    createdAt: new Date(profile.created_at),
    lastLogin: new Date()
  };
};

// Provider del contexto
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Verificar sesión existente al cargar
  useEffect(() => {
    const checkInitialSession = async () => {
      try {
        console.log('[AuthContext] Checking initial session...');
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('[AuthContext] Error getting session:', error);
          setIsLoading(false);
          return;
        }

        if (session?.user) {
          console.log('[AuthContext] Found existing session for user:', session.user.id);
          try {
            const userProfile = await fetchUserProfile(session.user.id);
            setUser(userProfile);
            setIsAuthenticated(true);
            console.log('[AuthContext] Initial session restored successfully');
          } catch (profileError) {
            console.error('[AuthContext] Error fetching profile for existing session:', profileError);
          }
        } else {
          console.log('[AuthContext] No existing session found');
        }
      } catch (error) {
        console.error('[AuthContext] Error checking initial session:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkInitialSession();

    // Escuchar cambios en la autenticación solo para logout
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event) => {
        console.log('[AuthContext] onAuthStateChange event:', event);
        if (event === 'SIGNED_OUT') {
          console.log('[AuthContext] User signed out, clearing state');
          setUser(null);
          setIsAuthenticated(false);
          setIsLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    console.log('[AuthContext] Starting login process...');
    setIsLoading(true);

    try {
      // 1. Autenticar con Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('No se pudo obtener información del usuario');
      }

      console.log('[AuthContext] Authentication successful, fetching profile...');

      // 2. Obtener el perfil del usuario directamente
      const userProfile = await fetchUserProfile(data.user.id);

      // 3. Actualizar el estado
      setUser(userProfile);
      setIsAuthenticated(true);

      console.log('[AuthContext] Login completed successfully');
    } catch (error) {
      console.error('[AuthContext] Login failed:', error);
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterData): Promise<void> => {
    console.log('[AuthContext] Starting registration process...');
    setIsLoading(true);

    try {
      const { data: authData, error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name,
            role: data.role || 'usuario'
          }
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!authData.user) {
        throw new Error('No se pudo crear el usuario');
      }

      console.log('[AuthContext] Registration successful');
    } catch (error) {
      console.error('[AuthContext] Registration failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      console.log('[AuthContext] Logging out...');
      await supabase.auth.signOut();
      // El estado se limpiará automáticamente a través del listener onAuthStateChange
    } catch (error) {
      console.error('[AuthContext] Error during logout:', error);
      // Forzar logout local si hay error
      setUser(null);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  };

  const hasRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return user ? roles.includes(user.role) : false;
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    hasRole,
    hasAnyRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
